{"version": 3, "sources": ["../../@tauri-apps/api/event.js"], "sourcesContent": ["import { invoke, transformCallback } from './core.js';\n\n// Copyright 2019-2024 Tauri Programme within The Commons Conservancy\n// SPDX-License-Identifier: Apache-2.0\n// SPDX-License-Identifier: MIT\n/**\n * The event system allows you to emit events to the backend and listen to events from it.\n *\n * This package is also accessible with `window.__TAURI__.event` when [`app.withGlobalTauri`](https://v2.tauri.app/reference/config/#withglobaltauri) in `tauri.conf.json` is set to `true`.\n * @module\n */\n/**\n * @since 1.1.0\n */\nvar TauriEvent;\n(function (TauriEvent) {\n    TauriEvent[\"WINDOW_RESIZED\"] = \"tauri://resize\";\n    TauriEvent[\"WINDOW_MOVED\"] = \"tauri://move\";\n    TauriEvent[\"WINDOW_CLOSE_REQUESTED\"] = \"tauri://close-requested\";\n    TauriEvent[\"WINDOW_DESTROYED\"] = \"tauri://destroyed\";\n    TauriEvent[\"WINDOW_FOCUS\"] = \"tauri://focus\";\n    TauriEvent[\"WINDOW_BLUR\"] = \"tauri://blur\";\n    TauriEvent[\"WINDOW_SCALE_FACTOR_CHANGED\"] = \"tauri://scale-change\";\n    TauriEvent[\"WINDOW_THEME_CHANGED\"] = \"tauri://theme-changed\";\n    TauriEvent[\"WINDOW_CREATED\"] = \"tauri://window-created\";\n    TauriEvent[\"WEBVIEW_CREATED\"] = \"tauri://webview-created\";\n    TauriEvent[\"DRAG_ENTER\"] = \"tauri://drag-enter\";\n    TauriEvent[\"DRAG_OVER\"] = \"tauri://drag-over\";\n    TauriEvent[\"DRAG_DROP\"] = \"tauri://drag-drop\";\n    TauriEvent[\"DRAG_LEAVE\"] = \"tauri://drag-leave\";\n})(TauriEvent || (TauriEvent = {}));\n/**\n * Unregister the event listener associated with the given name and id.\n *\n * @ignore\n * @param event The event name\n * @param eventId Event identifier\n * @returns\n */\nasync function _unlisten(event, eventId) {\n    await invoke('plugin:event|unlisten', {\n        event,\n        eventId\n    });\n}\n/**\n * Listen to an emitted event to any {@link EventTarget|target}.\n *\n * @example\n * ```typescript\n * import { listen } from '@tauri-apps/api/event';\n * const unlisten = await listen<string>('error', (event) => {\n *   console.log(`Got error, payload: ${event.payload}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @param options Event listening options.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function listen(event, handler, options) {\n    var _a;\n    const target = typeof (options === null || options === void 0 ? void 0 : options.target) === 'string'\n        ? { kind: 'AnyLabel', label: options.target }\n        : ((_a = options === null || options === void 0 ? void 0 : options.target) !== null && _a !== void 0 ? _a : { kind: 'Any' });\n    return invoke('plugin:event|listen', {\n        event,\n        target,\n        handler: transformCallback(handler)\n    }).then((eventId) => {\n        return async () => _unlisten(event, eventId);\n    });\n}\n/**\n * Listens once to an emitted event to any {@link EventTarget|target}.\n *\n * @example\n * ```typescript\n * import { once } from '@tauri-apps/api/event';\n * interface LoadedPayload {\n *   loggedIn: boolean,\n *   token: string\n * }\n * const unlisten = await once<LoadedPayload>('loaded', (event) => {\n *   console.log(`App is loaded, loggedIn: ${event.payload.loggedIn}, token: ${event.payload.token}`);\n * });\n *\n * // you need to call unlisten if your handler goes out of scope e.g. the component is unmounted\n * unlisten();\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param handler Event handler callback.\n * @param options Event listening options.\n * @returns A promise resolving to a function to unlisten to the event.\n * Note that removing the listener is required if your listener goes out of scope e.g. the component is unmounted.\n *\n * @since 1.0.0\n */\nasync function once(event, handler, options) {\n    return listen(event, (eventData) => {\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n        _unlisten(event, eventData.id);\n        handler(eventData);\n    }, options);\n}\n/**\n * Emits an event to all {@link EventTarget|targets}.\n *\n * @example\n * ```typescript\n * import { emit } from '@tauri-apps/api/event';\n * await emit('frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param payload Event payload.\n *\n * @since 1.0.0\n */\nasync function emit(event, payload) {\n    await invoke('plugin:event|emit', {\n        event,\n        payload\n    });\n}\n/**\n * Emits an event to all {@link EventTarget|targets} matching the given target.\n *\n * @example\n * ```typescript\n * import { emitTo } from '@tauri-apps/api/event';\n * await emitTo('main', 'frontend-loaded', { loggedIn: true, token: 'authToken' });\n * ```\n *\n * @param target Label of the target Window/Webview/WebviewWindow or raw {@link EventTarget} object.\n * @param event Event name. Must include only alphanumeric characters, `-`, `/`, `:` and `_`.\n * @param payload Event payload.\n *\n * @since 2.0.0\n */\nasync function emitTo(target, event, payload) {\n    const eventTarget = typeof target === 'string' ? { kind: 'AnyLabel', label: target } : target;\n    await invoke('plugin:event|emit_to', {\n        target: eventTarget,\n        event,\n        payload\n    });\n}\n\nexport { TauriEvent, emit, emitTo, listen, once };\n"], "mappings": ";;;;;;;AAcA,IAAI;AAAA,CACH,SAAUA,aAAY;AACnB,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,wBAAwB,IAAI;AACvC,EAAAA,YAAW,kBAAkB,IAAI;AACjC,EAAAA,YAAW,cAAc,IAAI;AAC7B,EAAAA,YAAW,aAAa,IAAI;AAC5B,EAAAA,YAAW,6BAA6B,IAAI;AAC5C,EAAAA,YAAW,sBAAsB,IAAI;AACrC,EAAAA,YAAW,gBAAgB,IAAI;AAC/B,EAAAA,YAAW,iBAAiB,IAAI;AAChC,EAAAA,YAAW,YAAY,IAAI;AAC3B,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,WAAW,IAAI;AAC1B,EAAAA,YAAW,YAAY,IAAI;AAC/B,GAAG,eAAe,aAAa,CAAC,EAAE;AASlC,eAAe,UAAU,OAAO,SAAS;AACrC,QAAM,OAAO,yBAAyB;AAAA,IAClC;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAuBA,eAAe,OAAO,OAAO,SAAS,SAAS;AAC3C,MAAI;AACJ,QAAM,SAAS,QAAQ,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,WACvF,EAAE,MAAM,YAAY,OAAO,QAAQ,OAAO,KACxC,KAAK,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,OAAO,SAAS,KAAK,EAAE,MAAM,MAAM;AAC9H,SAAO,OAAO,uBAAuB;AAAA,IACjC;AAAA,IACA;AAAA,IACA,SAAS,kBAAkB,OAAO;AAAA,EACtC,CAAC,EAAE,KAAK,CAAC,YAAY;AACjB,WAAO,YAAY,UAAU,OAAO,OAAO;AAAA,EAC/C,CAAC;AACL;AA2BA,eAAe,KAAK,OAAO,SAAS,SAAS;AACzC,SAAO,OAAO,OAAO,CAAC,cAAc;AAEhC,cAAU,OAAO,UAAU,EAAE;AAC7B,YAAQ,SAAS;AAAA,EACrB,GAAG,OAAO;AACd;AAeA,eAAe,KAAK,OAAO,SAAS;AAChC,QAAM,OAAO,qBAAqB;AAAA,IAC9B;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAgBA,eAAe,OAAO,QAAQ,OAAO,SAAS;AAC1C,QAAM,cAAc,OAAO,WAAW,WAAW,EAAE,MAAM,YAAY,OAAO,OAAO,IAAI;AACvF,QAAM,OAAO,wBAAwB;AAAA,IACjC,QAAQ;AAAA,IACR;AAAA,IACA;AAAA,EACJ,CAAC;AACL;", "names": ["TauriEvent"]}