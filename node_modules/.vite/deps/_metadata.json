{"hash": "31f7678d", "configHash": "82933b75", "lockfileHash": "75580b43", "browserHash": "c17a72fb", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "1d9da463", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "5fd87fc6", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "acf889b6", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "5b579acb", "needsInterop": true}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "97b5c20f", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "687329ce", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "36548893", "needsInterop": false}, "@tauri-apps/api/core": {"src": "../../@tauri-apps/api/core.js", "file": "@tauri-apps_api_core.js", "fileHash": "a6ab5077", "needsInterop": false}, "@tauri-apps/api/event": {"src": "../../@tauri-apps/api/event.js", "file": "@tauri-apps_api_event.js", "fileHash": "e8256897", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "31810346", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "1154f09c", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "8e625fac", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "77eba86b", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "4d790fd8", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "2e156403", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "3b4a459e", "needsInterop": false}}, "chunks": {"chunk-CJY6KJOW": {"file": "chunk-CJY6KJOW.js"}, "chunk-LQMYIA2Y": {"file": "chunk-LQMYIA2Y.js"}, "chunk-W4RVAGGZ": {"file": "chunk-W4RVAGGZ.js"}, "chunk-NXESFFTV": {"file": "chunk-NXESFFTV.js"}, "chunk-6PXSGDAH": {"file": "chunk-6PXSGDAH.js"}, "chunk-DRWLMN53": {"file": "chunk-DRWLMN53.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-G3PMV62Z": {"file": "chunk-G3PMV62Z.js"}}}