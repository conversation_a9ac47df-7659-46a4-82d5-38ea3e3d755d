{"rustc": 6560579996391851404, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 5347358027863023418, "path": 5146525947747086869, "deps": [[500211409582349667, "shared_child", false, 6326898528379192284], [1582828171158827377, "build_script_build", false, 14208747379722672216], [5986029879202738730, "log", false, 9300570882141189259], [9451456094439810778, "regex", false, 4588819508830529624], [9538054652646069845, "tokio", false, 353258954800503627], [9689903380558560274, "serde", false, 18265033257570559074], [10755362358622467486, "tauri", false, 12325468769855169647], [10806645703491011684, "thiserror", false, 16910409469864964697], [11337703028400419576, "os_pipe", false, 2127774713164514132], [14564311161534545801, "encoding_rs", false, 9789185580314248399], [15367738274754116744, "serde_json", false, 10553664108743005500], [16192041687293812804, "open", false, 15595216884820851817]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-824dd0c8102620e5/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}