{"rustc": 6560579996391851404, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\"]", "target": 12223948975794516716, "profile": 5347358027863023418, "path": 5082425167256031726, "deps": [[40386456601120721, "percent_encoding", false, 2474654511784584099], [442785307232013896, "tauri_runtime", false, 4162221602358464178], [1200537532907108615, "url<PERSON><PERSON>n", false, 10666322553851403149], [1386409696764982933, "objc2", false, 9888011659001728736], [3150220818285335163, "url", false, 7068394734155840421], [4143744114649553716, "raw_window_handle", false, 14289299676518318616], [4341921533227644514, "muda", false, 8818294217934391929], [4767930184903566869, "plist", false, 3299577775069018818], [4919829919303820331, "serialize_to_javascript", false, 11436534623494913652], [5986029879202738730, "log", false, 9300570882141189259], [7752760652095876438, "tauri_runtime_wry", false, 7633602256862680438], [8351317599104215083, "tray_icon", false, 16909801192647657588], [8589231650440095114, "embed_plist", false, 11256450309922512915], [9010263965687315507, "http", false, 1101455098096825628], [9228235415475680086, "tauri_macros", false, 15598283989882292970], [9538054652646069845, "tokio", false, 353258954800503627], [9689903380558560274, "serde", false, 18265033257570559074], [9859211262912517217, "objc2_foundation", false, 7403343785091592436], [9920160576179037441, "getrandom", false, 6131949899052042874], [10229185211513642314, "mime", false, 14528631000580722458], [10575598148575346675, "objc2_app_kit", false, 16113764909902968722], [10629569228670356391, "futures_util", false, 4070443668592924580], [10755362358622467486, "build_script_build", false, 15189701765632813222], [10806645703491011684, "thiserror", false, 16910409469864964697], [11050281405049894993, "tauri_utils", false, 10026286456544799870], [11989259058781683633, "dunce", false, 5423799803614083760], [12565293087094287914, "window_vibrancy", false, 16147935258440651217], [12986574360607194341, "serde_repr", false, 7658569833401521158], [13077543566650298139, "heck", false, 14414243282818615259], [13625485746686963219, "anyhow", false, 1239931696919690419], [15367738274754116744, "serde_json", false, 10553664108743005500], [16928111194414003569, "dirs", false, 6015725861238064161], [17155886227862585100, "glob", false, 11736690090176539966]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-6d57b9b38c16fb54/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}