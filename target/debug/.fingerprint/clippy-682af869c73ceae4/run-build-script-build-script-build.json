{"rustc": 6560579996391851404, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[12651179010648504719, "build_script_build", false, 12073339295970383861], [10755362358622467486, "build_script_build", false, 15189701765632813222], [3935545708480822364, "build_script_build", false, 8256003861364272852], [1582828171158827377, "build_script_build", false, 14208747379722672216]], "local": [{"RerunIfChanged": {"output": "debug/build/clippy-682af869c73ceae4/output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}