{"rustc": 6560579996391851404, "features": "[\"alloc\", \"ansi\", \"default\", \"fmt\", \"nu-ansi-term\", \"registry\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"tracing-log\"]", "declared_features": "[\"alloc\", \"ansi\", \"chrono\", \"default\", \"env-filter\", \"fmt\", \"json\", \"local-time\", \"matchers\", \"nu-ansi-term\", \"once_cell\", \"parking_lot\", \"regex\", \"registry\", \"serde\", \"serde_json\", \"sharded-slab\", \"smallvec\", \"std\", \"thread_local\", \"time\", \"tracing\", \"tracing-log\", \"tracing-serde\", \"valuable\", \"valuable-serde\", \"valuable_crate\"]", "target": 4817557058868189149, "profile": 1006155289083248400, "path": 14900606905784426181, "deps": [[1017461770342116999, "sharded_slab", false, 12078992867610570905], [3424551429995674438, "tracing_core", false, 4415561864158397567], [3666196340704888985, "smallvec", false, 2873008418798999949], [8614575489689151157, "nu_ansi_term", false, 12902569438422406923], [10806489435541507125, "tracing_log", false, 10490960846428251571], [12427285511609802057, "thread_local", false, 4304082218466335849]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tracing-subscriber-fa3486201e1f9fa2/dep-lib-tracing_subscriber", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}