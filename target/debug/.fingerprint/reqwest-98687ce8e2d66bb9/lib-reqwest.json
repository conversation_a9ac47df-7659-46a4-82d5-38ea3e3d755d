{"rustc": 6560579996391851404, "features": "[\"__rustls\", \"__tls\", \"hyper-rustls\", \"rustls\", \"rustls-native-certs\", \"rustls-tls-native-roots\", \"stream\", \"tokio-rustls\", \"tokio-util\", \"wasm-streams\"]", "declared_features": "[\"__internal_proxy_sys_no_cache\", \"__rustls\", \"__tls\", \"async-compression\", \"blocking\", \"brotli\", \"cookie_crate\", \"cookie_store\", \"cookies\", \"default\", \"default-tls\", \"deflate\", \"futures-channel\", \"gzip\", \"h3\", \"h3-quinn\", \"hickory-dns\", \"hickory-resolver\", \"http3\", \"hyper-rustls\", \"hyper-tls\", \"json\", \"mime_guess\", \"multipart\", \"native-tls\", \"native-tls-alpn\", \"native-tls-crate\", \"native-tls-vendored\", \"quinn\", \"rustls\", \"rustls-native-certs\", \"rustls-tls\", \"rustls-tls-manual-roots\", \"rustls-tls-native-roots\", \"rustls-tls-webpki-roots\", \"serde_json\", \"socks\", \"stream\", \"tokio-native-tls\", \"tokio-rustls\", \"tokio-socks\", \"tokio-util\", \"trust-dns\", \"wasm-streams\", \"webpki-roots\"]", "target": 16585426341985349207, "profile": 5347358027863023418, "path": 4061014987415285955, "deps": [[40386456601120721, "percent_encoding", false, 2474654511784584099], [95042085696191081, "ipnet", false, 2097239539818328009], [264090853244900308, "sync_wrapper", false, 13793246713100459400], [784494742817713399, "tower_service", false, 17494299998476258607], [1044435446100926395, "hyper_rustls", false, 1489900074844704598], [1288403060204016458, "tokio_util", false, 16665779152138808781], [1906322745568073236, "pin_project_lite", false, 15968202698829023668], [3150220818285335163, "url", false, 7068394734155840421], [3722963349756955755, "once_cell", false, 7515198253589974185], [4405182208873388884, "http", false, 10861615716676178807], [5986029879202738730, "log", false, 9300570882141189259], [7414427314941361239, "hyper", false, 8217942748144098421], [7620660491849607393, "futures_core", false, 8046200181839481027], [8915503303801890683, "http_body", false, 1431669528390480412], [9538054652646069845, "tokio", false, 353258954800503627], [9689903380558560274, "serde", false, 18265033257570559074], [10229185211513642314, "mime", false, 14528631000580722458], [10629569228670356391, "futures_util", false, 4070443668592924580], [11107720164717273507, "system_configuration", false, 4414921798203155593], [11295624341523567602, "rustls", false, 311684512573481003], [13809605890706463735, "h2", false, 12525827707604872439], [14394652928131349565, "rustls_native_certs", false, 17423534511294031903], [14564311161534545801, "encoding_rs", false, 9789185580314248399], [16066129441945555748, "bytes", false, 13079279578986384596], [16311359161338405624, "rustls_pemfile", false, 2058974299725807654], [16542808166767769916, "serde_urlencoded", false, 18325782788467592163], [16622232390123975175, "tokio_rustls", false, 16071175041417792964], [18066890886671768183, "base64", false, 132961652928832079]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/reqwest-98687ce8e2d66bb9/dep-lib-reqwest", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}