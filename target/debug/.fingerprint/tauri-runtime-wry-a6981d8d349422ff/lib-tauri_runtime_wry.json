{"rustc": 6560579996391851404, "features": "[]", "declared_features": "[\"devtools\", \"macos-private-api\", \"macos-proxy\", \"objc-exception\", \"tracing\", \"unstable\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 10400821898230421430, "deps": [[442785307232013896, "tauri_runtime", false, 4162221602358464178], [1386409696764982933, "objc2", false, 9888011659001728736], [3150220818285335163, "url", false, 7068394734155840421], [4143744114649553716, "raw_window_handle", false, 14289299676518318616], [5986029879202738730, "log", false, 9300570882141189259], [7752760652095876438, "build_script_build", false, 16805513280336818768], [9010263965687315507, "http", false, 1101455098096825628], [9859211262912517217, "objc2_foundation", false, 7403343785091592436], [10575598148575346675, "objc2_app_kit", false, 16113764909902968722], [11050281405049894993, "tauri_utils", false, 10026286456544799870], [13223659721939363523, "tao", false, 5412890479637338931], [14794439852947137341, "wry", false, 4067723813664257948]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-a6981d8d349422ff/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}