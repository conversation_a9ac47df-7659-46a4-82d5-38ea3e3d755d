["/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/basename.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/dirname.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/extname.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/is_absolute.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/join.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/normalize.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/resolve.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/commands/resolve_directory.toml", "/Users/<USER>/Projects/clippy/target/debug/build/tauri-acbaf3686767d478/out/permissions/path/autogenerated/default.toml"]