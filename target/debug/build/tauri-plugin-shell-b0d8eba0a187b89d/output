cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Projects/clippy/target/debug/build/tauri-plugin-shell-b0d8eba0a187b89d/out/tauri-plugin-shell-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=/Users/<USER>/Projects/clippy/target/debug/build/tauri-plugin-shell-b0d8eba0a187b89d/out/global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tauri-plugin-shell-2.2.1/api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
