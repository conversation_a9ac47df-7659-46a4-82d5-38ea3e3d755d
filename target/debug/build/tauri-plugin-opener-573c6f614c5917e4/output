cargo:rerun-if-changed=permissions
cargo:PERMISSION_FILES_PATH=/Users/<USER>/Projects/clippy/target/debug/build/tauri-plugin-opener-573c6f614c5917e4/out/tauri-plugin-opener-permission-files
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:GLOBAL_SCOPE_SCHEMA_PATH=/Users/<USER>/Projects/clippy/target/debug/build/tauri-plugin-opener-573c6f614c5917e4/out/global-scope.json
cargo:GLOBAL_API_SCRIPT_PATH=/Users/<USER>/.cargo/registry/src/rsproxy.cn-e3de039b2554c837/tauri-plugin-opener-2.2.7/api-iife.js
cargo:rustc-check-cfg=cfg(mobile)
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
