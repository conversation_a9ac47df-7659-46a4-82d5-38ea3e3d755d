@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 220 13% 13%;
    --card: 0 0% 100%;
    --card-foreground: 220 13% 13%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 13%;
    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 13% 95%;
    --secondary-foreground: 220 13% 13%;
    --muted: 220 13% 95%;
    --muted-foreground: 220 9% 46%;
    --accent: 262 83% 58%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262 83% 58%;
    --radius: 0.75rem;

    /* Material Design Extended Colors */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 32 95% 44%;
    --warning-foreground: 355 100% 97%;
    --info: 199 89% 48%;
    --info-foreground: 355 100% 97%;

    /* Surface Colors */
    --surface: 0 0% 100%;
    --surface-variant: 220 13% 95%;
    --on-surface: 220 13% 13%;
    --on-surface-variant: 220 9% 46%;

    /* Elevation Colors */
    --elevation-1: 0 0% 100%;
    --elevation-2: 0 0% 98%;
    --elevation-3: 0 0% 96%;
  }

  .dark {
    --background: 220 13% 9%;
    --foreground: 220 13% 95%;
    --card: 220 13% 9%;
    --card-foreground: 220 13% 95%;
    --popover: 220 13% 9%;
    --popover-foreground: 220 13% 95%;
    --primary: 262 83% 58%;
    --primary-foreground: 0 0% 100%;
    --secondary: 220 13% 15%;
    --secondary-foreground: 220 13% 95%;
    --muted: 220 13% 15%;
    --muted-foreground: 220 9% 65%;
    --accent: 262 83% 58%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13% 15%;
    --input: 220 13% 15%;
    --ring: 262 83% 58%;

    /* Material Design Extended Colors - Dark */
    --success: 142 76% 36%;
    --success-foreground: 355 100% 97%;
    --warning: 32 95% 44%;
    --warning-foreground: 355 100% 97%;
    --info: 199 89% 48%;
    --info-foreground: 355 100% 97%;

    /* Surface Colors - Dark */
    --surface: 220 13% 9%;
    --surface-variant: 220 13% 15%;
    --on-surface: 220 13% 95%;
    --on-surface-variant: 220 9% 65%;

    /* Elevation Colors - Dark */
    --elevation-1: 220 13% 11%;
    --elevation-2: 220 13% 13%;
    --elevation-3: 220 13% 15%;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .material-elevation-1 {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  }

  .material-elevation-2 {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
  }

  .material-elevation-3 {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23);
  }

  .material-transition {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .material-ripple {
    position: relative;
    overflow: hidden;
  }

  .material-ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: currentColor;
    opacity: 0.1;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  .material-ripple:active::before {
    width: 300px;
    height: 300px;
  }

  /* Material Design Animations */
  .material-fade-in {
    animation: materialFadeIn 0.3s ease-out;
  }

  .material-slide-up {
    animation: materialSlideUp 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .material-scale-in {
    animation: materialScaleIn 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .material-bounce {
    animation: materialBounce 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  }

  /* Hover Effects */
  .material-hover {
    transition: all 0.2s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .material-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  /* Focus States */
  .material-focus:focus-visible {
    outline: 2px solid hsl(var(--primary));
    outline-offset: 2px;
    box-shadow: 0 0 0 4px hsl(var(--primary) / 0.1);
  }

  /* Loading States */
  .material-loading {
    position: relative;
    overflow: hidden;
  }

  .material-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    animation: materialShimmer 1.5s infinite;
  }

  /* Pulse Effect */
  .material-pulse {
    animation: materialPulse 2s infinite;
  }

  /* Floating Action Button */
  .material-fab {
    position: fixed;
    bottom: 24px;
    right: 24px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
  }

  .material-fab:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  }

  /* Card Hover Effects */
  .material-card-hover {
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    cursor: pointer;
  }

  .material-card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  }

  /* Smooth Scrolling */
  .material-scroll {
    scroll-behavior: smooth;
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }

  .material-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .material-scroll::-webkit-scrollbar-track {
    background: transparent;
  }

  .material-scroll::-webkit-scrollbar-thumb {
    background: hsl(var(--muted));
    border-radius: 3px;
  }

  .material-scroll::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground));
  }
}

/* Keyframe Animations */
@keyframes materialFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes materialSlideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes materialScaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes materialBounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes materialShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes materialPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  .material-elevation-1 {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3), 0 1px 2px rgba(0, 0, 0, 0.4);
  }

  .material-elevation-2 {
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4), 0 3px 6px rgba(0, 0, 0, 0.5);
  }

  .material-elevation-3 {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5), 0 6px 6px rgba(0, 0, 0, 0.6);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .material-fab {
    bottom: 16px;
    right: 16px;
    width: 48px;
    height: 48px;
  }

  .material-card-hover:hover {
    transform: none;
  }

  .material-hover:hover {
    transform: none;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .material-transition,
  .material-fade-in,
  .material-slide-up,
  .material-scale-in,
  .material-bounce,
  .material-hover,
  .material-card-hover {
    animation: none;
    transition: none;
  }

  .material-loading::after {
    animation: none;
  }

  .material-pulse {
    animation: none;
  }
}
