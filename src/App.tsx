import { useState, useEffect } from "react";
import { invoke } from "@tauri-apps/api/core";
import { listen } from "@tauri-apps/api/event";
import { ClipboardManager } from "./components/ClipboardManager";
import { StorageConfig } from "./components/StorageConfig";
import { ConfigGuide } from "./components/ConfigGuide";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Toaster } from "@/components/ui/sonner";
import { ClipboardList, Settings, BookOpen } from "lucide-react";
import "./App.css";

export interface FileTypeInfo {
  path: string;
  file_type: string;
  mime_type: string;
  category: string;
}

export interface ClipboardItem {
  id: string;
  content: string;
  timestamp: number;
  item_type: string; // "text" 或 "files"
  size?: number;
  file_paths?: string[];
  file_types?: FileTypeInfo[];
}

export interface StorageStats {
  total_items: number;
  deleted_items: number;
  file_size: number;
}

function App() {
  const [activeTab, setActiveTab] = useState<string>('clipboard');
  const [clipboardHistory, setClipboardHistory] = useState<ClipboardItem[]>([]);
  const [storageStats, setStorageStats] = useState<StorageStats | null>(null);

  useEffect(() => {
    // 获取初始剪贴板历史
    const fetchHistory = async () => {
      try {
        const history = await invoke<ClipboardItem[]>("get_clipboard_history");
        setClipboardHistory(history);
      } catch (error) {
        console.error("Failed to fetch clipboard history:", error);
      }
    };

    // 获取存储统计信息
    const fetchStats = async () => {
      try {
        const stats = await invoke<StorageStats>("get_storage_stats");
        setStorageStats(stats);
      } catch (error) {
        console.error("Failed to fetch storage stats:", error);
      }
    };

    fetchHistory();
    fetchStats();

    // 监听剪贴板更新事件
    const unlisten = listen<ClipboardItem>("clipboard-update", (event) => {
      setClipboardHistory((prev) => [event.payload, ...prev.slice(0, 99)]);
    });

    return () => {
      unlisten.then((fn) => fn());
    };
  }, []);

  const handleClearHistory = async () => {
    try {
      // 优先使用混合同步清空（包含同步）
      try {
        await invoke("hybrid_sync_clear_all");
      } catch (hybridError) {
        // 如果混合同步清空失败，回退到传统清空
        console.warn("混合同步清空失败，使用传统清空:", hybridError);
        await invoke("clear_clipboard_history");
      }
      
      setClipboardHistory([]);
      // 重新获取统计信息
      const stats = await invoke<StorageStats>("get_storage_stats");
      setStorageStats(stats);
    } catch (error) {
      console.error("Failed to clear clipboard history:", error);
    }
  };

  const handleDeleteItem = async (itemId: string) => {
    try {
      // 优先使用混合同步删除（包含同步）
      try {
        await invoke("hybrid_sync_delete_item", { itemId });
      } catch (hybridError) {
        // 如果混合同步删除失败，回退到传统删除
        console.warn("混合同步删除失败，使用传统删除:", hybridError);
        await invoke("delete_clipboard_item", { itemId });
      }
      
      // 从本地状态中移除项目
      setClipboardHistory(prev => prev.filter(item => item.id !== itemId));
      
      // 重新获取统计信息
      const stats = await invoke<StorageStats>("get_storage_stats");
      setStorageStats(stats);
    } catch (error) {
      console.error("Failed to delete clipboard item:", error);
    }
  };

  const handleCompactStorage = async () => {
    try {
      await invoke("compact_storage");
      // 重新获取统计信息
      const stats = await invoke<StorageStats>("get_storage_stats");
      setStorageStats(stats);
    } catch (error) {
      console.error("Failed to compact storage:", error);
    }
  };

  const handleCopyToClipboard = async (content: string) => {
    try {
      await invoke("copy_to_clipboard", { content });
    } catch (error) {
      console.error("Failed to copy to clipboard:", error);
    }
  };

  const handleCopyImageToClipboard = async (base64Data: string) => {
    try {
      await invoke("copy_image_to_clipboard", { base64Data });
    } catch (error) {
      console.error("Failed to copy image to clipboard:", error);
    }
  };

  const handleCopyFilesToClipboard = async (filePaths: string[]) => {
    try {
      await invoke("copy_files_to_clipboard", { filePaths });
    } catch (error) {
      console.error("Failed to copy files to clipboard:", error);
    }
  };

  const handleSync = async () => {
    try {
      await invoke("hybrid_sync_now");
    } catch (error) {
      console.error("Failed to sync:", error);
      throw error; // 重新抛出错误以便组件处理
    }
  };



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="container mx-auto p-6 max-w-7xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2">
            Clippy
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            智能剪贴板管理工具
          </p>
        </div>

        <Card className="shadow-xl border-0 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm">
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3 bg-slate-100/50 dark:bg-slate-700/50 p-1 h-14">
                <TabsTrigger
                  value="clipboard"
                  className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-slate-800"
                >
                  <ClipboardList className="h-4 w-4" />
                  剪贴板历史
                </TabsTrigger>
                <TabsTrigger
                  value="config"
                  className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-slate-800"
                >
                  <Settings className="h-4 w-4" />
                  同步配置
                </TabsTrigger>
                <TabsTrigger
                  value="guide"
                  className="flex items-center gap-2 text-sm font-medium data-[state=active]:bg-white data-[state=active]:shadow-sm dark:data-[state=active]:bg-slate-800"
                >
                  <BookOpen className="h-4 w-4" />
                  配置指南
                </TabsTrigger>
              </TabsList>

              <div className="p-6">
                <TabsContent value="clipboard" className="mt-0">
                  <ClipboardManager
                    items={clipboardHistory}
                    storageStats={storageStats}
                    onClearHistory={handleClearHistory}
                    onDeleteItem={handleDeleteItem}
                    onCompactStorage={handleCompactStorage}
                    onCopyToClipboard={handleCopyToClipboard}
                    onCopyImageToClipboard={handleCopyImageToClipboard}
                    onCopyFilesToClipboard={handleCopyFilesToClipboard}
                    onSync={handleSync}
                  />
                </TabsContent>

                <TabsContent value="config" className="mt-0">
                  <StorageConfig />
                </TabsContent>

                <TabsContent value="guide" className="mt-0">
                  <ConfigGuide />
                </TabsContent>
              </div>
            </Tabs>
          </CardContent>
        </Card>
      </div>

      <Toaster />
    </div>
  );
}

export default App;
