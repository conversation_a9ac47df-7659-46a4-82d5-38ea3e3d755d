import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import {
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2,
  Cloud,
  Database,
  Settings,
  TestTube,
  Save,
  RefreshCw,
  HardDrive,
  Globe,
  Lock,
  Key
} from 'lucide-react';

type StorageBackend = 
  | { type: "FileSystem"; root_path: string }
  | { type: "S3"; bucket: string; region: string; access_key_id: string; secret_access_key: string; endpoint?: string }
  | { type: "S3Compatible"; bucket: string; endpoint: string; access_key_id: string; secret_access_key: string; region?: string }
  | { type: "Oss"; bucket: string; endpoint: string; access_key_id: string; access_key_secret: string }
  | { type: "Cos"; bucket: string; endpoint: string; secret_id: string; secret_key: string }
  | { type: "AzBlob"; container: string; account_name: string; account_key: string };

interface StorageConfig {
  backend: StorageBackend;
  retry_attempts: number;
  timeout_seconds: number;
}

interface SyncStatus {
  item_count: number;
  is_syncing: boolean;
}

export const StorageConfig: React.FC = () => {
  const [config, setConfig] = useState<StorageConfig>({
    backend: { type: "FileSystem", root_path: './clippy_sync_data' },
    retry_attempts: 3,
    timeout_seconds: 30,
  });

  const [backendType, setBackendType] = useState<string>('FileSystem');
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [isAdvancedMode, setIsAdvancedMode] = useState(false);

  useEffect(() => {
    loadConfig();
    loadSyncStatus();
  }, []);

  const loadConfig = async () => {
    try {
      const currentConfig = await invoke<any>('get_storage_config');
      setConfig(currentConfig);
      
      // 确定当前的后端类型
      const backend = currentConfig.backend;
      setBackendType(backend.type);
    } catch (error) {
      console.error('Failed to load config:', error);
      setMessage({ type: 'error', text: '加载配置失败' });
    }
  };

  const loadSyncStatus = async () => {
    try {
      const status = await invoke<SyncStatus>('get_sync_status');
      setSyncStatus(status);
    } catch (error) {
      console.error('Failed to load sync status:', error);
    }
  };

  const saveConfig = async () => {
    setIsLoading(true);

    try {
      await invoke('configure_storage', { config: config });
      toast.success('配置保存成功！重启应用后生效。');
      await loadSyncStatus();
    } catch (error) {
      toast.error(`保存失败: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  const testConnection = async () => {
    setIsTesting(true);

    try {
      const result = await invoke<boolean>('test_storage_connection', { config: config });
      if (result) {
        toast.success('连接测试成功！存储配置有效。');
      } else {
        toast.error('连接测试失败！请检查配置。');
      }
    } catch (error) {
      toast.error(`连接测试失败: ${error}`);
    } finally {
      setIsTesting(false);
    }
  };

  const syncNow = async () => {
    try {
      await invoke('sync_now');
      setMessage({ type: 'success', text: '同步完成！' });
      await loadSyncStatus();
    } catch (error) {
      setMessage({ type: 'error', text: `同步失败: ${error}` });
    }
  };

  const updateBackend = (newBackendType: string) => {
    setBackendType(newBackendType);
    
    // 创建新的后端配置
    let newBackend: StorageBackend;
    
    switch (newBackendType) {
      case 'FileSystem':
        newBackend = { type: "FileSystem", root_path: './clippy_sync_data' };
        break;
      case 'S3':
        newBackend = {
          type: "S3",
          bucket: '',
          region: 'us-east-1',
          access_key_id: '',
          secret_access_key: '',
          endpoint: undefined,
        };
        break;
      case 'S3Compatible':
        newBackend = {
          type: "S3Compatible",
          bucket: '',
          endpoint: '',
          access_key_id: '',
          secret_access_key: '',
          region: 'us-east-1',
        };
        break;
      case 'Oss':
        newBackend = {
          type: "Oss",
          bucket: '',
          endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
          access_key_id: '',
          access_key_secret: '',
        };
        break;
      case 'Cos':
        newBackend = {
          type: "Cos",
          bucket: '',
          endpoint: 'https://cos.ap-guangzhou.myqcloud.com',
          secret_id: '',
          secret_key: '',
        };
        break;
      case 'AzBlob':
        newBackend = {
          type: "AzBlob",
          container: '',
          account_name: '',
          account_key: '',
        };
        break;
      default:
        newBackend = { type: "FileSystem", root_path: './clippy_sync_data' };
    }
    
    setConfig(prev => ({ ...prev, backend: newBackend }));
  };

  const updateBackendField = (field: string, value: string) => {
    setConfig(prev => ({
      ...prev,
      backend: {
        ...prev.backend,
        [field]: value === '' ? undefined : value,
      },
    }));
  };

  const renderBackendFields = () => {
    const backend = config.backend;
    
    if (backend.type !== backendType) return null;

    // Helper function to get field value safely
    const getFieldValue = (field: string): string => {
      return (backend as any)[field] || '';
    };

    switch (backend.type) {
      case 'FileSystem':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="root_path">存储路径</Label>
              <Input
                id="root_path"
                value={getFieldValue('root_path')}
                onChange={(e) => updateBackendField('root_path', e.target.value)}
                placeholder="./clippy_sync_data"
              />
            </div>
          </div>
        );

      case 'S3':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="bucket">存储桶名称</Label>
              <Input
                id="bucket"
                value={getFieldValue('bucket')}
                onChange={(e) => updateBackendField('bucket', e.target.value)}
                placeholder="my-clippy-bucket"
              />
            </div>
            <div>
              <Label htmlFor="region">区域</Label>
              <Input
                id="region"
                value={getFieldValue('region')}
                onChange={(e) => updateBackendField('region', e.target.value)}
                placeholder="us-east-1"
              />
            </div>
            <div>
              <Label htmlFor="access_key_id">访问密钥ID</Label>
              <Input
                id="access_key_id"
                value={getFieldValue('access_key_id')}
                onChange={(e) => updateBackendField('access_key_id', e.target.value)}
                placeholder="AKIAIOSFODNN7EXAMPLE"
              />
            </div>
            <div>
              <Label htmlFor="secret_access_key">私有访问密钥</Label>
              <Input
                id="secret_access_key"
                type="password"
                value={getFieldValue('secret_access_key') === '***' ? '' : getFieldValue('secret_access_key')}
                onChange={(e) => updateBackendField('secret_access_key', e.target.value)}
                placeholder={getFieldValue('secret_access_key') === '***' ? '已设置 (输入新值以更改)' : ''}
              />
            </div>
            <div>
              <Label htmlFor="endpoint">自定义端点 (可选)</Label>
              <Input
                id="endpoint"
                value={getFieldValue('endpoint')}
                onChange={(e) => updateBackendField('endpoint', e.target.value)}
                placeholder="https://s3.amazonaws.com"
              />
            </div>
          </div>
        );

      case 'S3Compatible':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="bucket">存储桶名称</Label>
              <Input
                id="bucket"
                value={getFieldValue('bucket')}
                onChange={(e) => updateBackendField('bucket', e.target.value)}
                placeholder="clippy"
              />
            </div>
            <div>
              <Label htmlFor="endpoint">端点地址</Label>
              <Input
                id="endpoint"
                value={getFieldValue('endpoint')}
                onChange={(e) => updateBackendField('endpoint', e.target.value)}
                placeholder="http://localhost:9000"
              />
            </div>
            <div>
              <Label htmlFor="access_key_id">访问密钥</Label>
              <Input
                id="access_key_id"
                value={getFieldValue('access_key_id')}
                onChange={(e) => updateBackendField('access_key_id', e.target.value)}
                placeholder="admin"
              />
            </div>
            <div>
              <Label htmlFor="secret_access_key">私有密钥</Label>
              <Input
                id="secret_access_key"
                type="password"
                value={getFieldValue('secret_access_key') === '***' ? '' : getFieldValue('secret_access_key')}
                onChange={(e) => updateBackendField('secret_access_key', e.target.value)}
                placeholder={getFieldValue('secret_access_key') === '***' ? '已设置 (输入新值以更改)' : 'password123'}
              />
            </div>
            <div>
              <Label htmlFor="region">区域 (可选)</Label>
              <Input
                id="region"
                value={getFieldValue('region')}
                onChange={(e) => updateBackendField('region', e.target.value)}
                placeholder="us-east-1"
              />
            </div>
          </div>
        );

      case 'Oss':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="bucket">存储桶名称</Label>
              <Input
                id="bucket"
                value={getFieldValue('bucket')}
                onChange={(e) => updateBackendField('bucket', e.target.value)}
                placeholder="my-clippy-bucket"
              />
            </div>
            <div>
              <Label htmlFor="endpoint">端点地址</Label>
              <Input
                id="endpoint"
                value={getFieldValue('endpoint')}
                onChange={(e) => updateBackendField('endpoint', e.target.value)}
                placeholder="https://oss-cn-hangzhou.aliyuncs.com"
              />
            </div>
            <div>
              <Label htmlFor="access_key_id">访问密钥ID</Label>
              <Input
                id="access_key_id"
                value={getFieldValue('access_key_id')}
                onChange={(e) => updateBackendField('access_key_id', e.target.value)}
                placeholder="LTAI4G***"
              />
            </div>
            <div>
              <Label htmlFor="access_key_secret">访问密钥</Label>
              <Input
                id="access_key_secret"
                type="password"
                value={getFieldValue('access_key_secret') === '***' ? '' : getFieldValue('access_key_secret')}
                onChange={(e) => updateBackendField('access_key_secret', e.target.value)}
                placeholder={getFieldValue('access_key_secret') === '***' ? '已设置 (输入新值以更改)' : ''}
              />
            </div>
          </div>
        );

      case 'Cos':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="bucket">存储桶名称</Label>
              <Input
                id="bucket"
                value={getFieldValue('bucket')}
                onChange={(e) => updateBackendField('bucket', e.target.value)}
                placeholder="my-clippy-bucket-1234567890"
              />
            </div>
            <div>
              <Label htmlFor="endpoint">端点地址</Label>
              <Input
                id="endpoint"
                value={getFieldValue('endpoint')}
                onChange={(e) => updateBackendField('endpoint', e.target.value)}
                placeholder="https://cos.ap-guangzhou.myqcloud.com"
              />
            </div>
            <div>
              <Label htmlFor="secret_id">密钥ID</Label>
              <Input
                id="secret_id"
                value={getFieldValue('secret_id')}
                onChange={(e) => updateBackendField('secret_id', e.target.value)}
                placeholder="AKIDrAr7***"
              />
            </div>
            <div>
              <Label htmlFor="secret_key">密钥</Label>
              <Input
                id="secret_key"
                type="password"
                value={getFieldValue('secret_key') === '***' ? '' : getFieldValue('secret_key')}
                onChange={(e) => updateBackendField('secret_key', e.target.value)}
                placeholder={getFieldValue('secret_key') === '***' ? '已设置 (输入新值以更改)' : ''}
              />
            </div>
          </div>
        );

      case 'AzBlob':
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="container">容器名称</Label>
              <Input
                id="container"
                value={getFieldValue('container')}
                onChange={(e) => updateBackendField('container', e.target.value)}
                placeholder="clippy"
              />
            </div>
            <div>
              <Label htmlFor="account_name">存储账户名</Label>
              <Input
                id="account_name"
                value={getFieldValue('account_name')}
                onChange={(e) => updateBackendField('account_name', e.target.value)}
                placeholder="mystorageaccount"
              />
            </div>
            <div>
              <Label htmlFor="account_key">账户密钥</Label>
              <Input
                id="account_key"
                type="password"
                value={getFieldValue('account_key') === '***' ? '' : getFieldValue('account_key')}
                onChange={(e) => updateBackendField('account_key', e.target.value)}
                placeholder={getFieldValue('account_key') === '***' ? '已设置 (输入新值以更改)' : ''}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-foreground">存储配置</h2>
          <p className="text-muted-foreground">配置云存储以实现跨设备同步</p>
        </div>
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <Switch
              checked={isAdvancedMode}
              onCheckedChange={setIsAdvancedMode}
            />
            <Label className="text-sm">高级模式</Label>
          </div>
        </div>
      </div>

      {/* Sync Status Card */}
      {syncStatus && (
        <Card className="material-elevation-1">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Cloud className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1">
                <p className="font-medium">同步状态</p>
                <p className="text-sm text-muted-foreground">
                  {syncStatus.item_count} 个项目已同步
                  {syncStatus.is_syncing && " • 正在同步..."}
                </p>
              </div>
              {syncStatus.is_syncing && (
                <div className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4 animate-spin text-primary" />
                  <span className="text-sm text-primary">同步中</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Configuration Card */}
      <Card className="material-elevation-1">
        <CardHeader className="pb-4">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Settings className="h-5 w-5 text-primary" />
            </div>
            <div>
              <CardTitle>存储后端配置</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                选择并配置你的云存储服务
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Backend Type Selection */}
          <div className="space-y-3">
            <Label className="text-base font-medium flex items-center gap-2">
              <Database className="h-4 w-4" />
              存储后端类型
            </Label>
            <Select value={backendType} onValueChange={updateBackend}>
              <SelectTrigger className="h-12">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="FileSystem">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4" />
                    本地文件系统
                  </div>
                </SelectItem>
                <SelectItem value="S3Compatible">
                  <div className="flex items-center gap-2">
                    <Cloud className="h-4 w-4" />
                    MinIO / S3兼容
                  </div>
                </SelectItem>
                <SelectItem value="S3">
                  <div className="flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    Amazon S3
                  </div>
                </SelectItem>
                <SelectItem value="Oss">
                  <div className="flex items-center gap-2">
                    <Cloud className="h-4 w-4" />
                    阿里云 OSS
                  </div>
                </SelectItem>
                <SelectItem value="Cos">
                  <div className="flex items-center gap-2">
                    <Cloud className="h-4 w-4" />
                    腾讯云 COS
                  </div>
                </SelectItem>
                <SelectItem value="AzBlob">
                  <div className="flex items-center gap-2">
                    <Cloud className="h-4 w-4" />
                    Azure Blob Storage
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Separator />

          {/* Backend Configuration Fields */}
          <div className="space-y-4">
            {renderBackendFields()}
          </div>

          {/* Advanced Settings */}
          {isAdvancedMode && (
            <>
              <Separator />
              <div className="space-y-4">
                <Label className="text-base font-medium flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  高级设置
                </Label>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="retry_attempts">重试次数</Label>
                    <Input
                      id="retry_attempts"
                      type="number"
                      value={config.retry_attempts}
                      onChange={(e) => setConfig(prev => ({ ...prev, retry_attempts: parseInt(e.target.value) || 3 }))}
                      min="1"
                      max="10"
                      className="h-10"
                    />
                    <p className="text-xs text-muted-foreground">连接失败时的重试次数 (1-10)</p>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="timeout_seconds">超时时间 (秒)</Label>
                    <Input
                      id="timeout_seconds"
                      type="number"
                      value={config.timeout_seconds}
                      onChange={(e) => setConfig(prev => ({ ...prev, timeout_seconds: parseInt(e.target.value) || 30 }))}
                      min="5"
                      max="300"
                      className="h-10"
                    />
                    <p className="text-xs text-muted-foreground">网络请求超时时间 (5-300秒)</p>
                  </div>
                </div>
              </div>
            </>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              onClick={testConnection}
              disabled={isTesting}
              variant="outline"
              className="flex items-center gap-2 h-11"
            >
              {isTesting ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <TestTube className="h-4 w-4" />
              )}
              {isTesting ? "测试中..." : "测试连接"}
            </Button>
            <Button
              onClick={saveConfig}
              disabled={isLoading}
              className="flex items-center gap-2 h-11"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              {isLoading ? "保存中..." : "保存配置"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
