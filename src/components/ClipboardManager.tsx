import React, { useState } from "react";
import {
  Copy,
  Trash2,
  Clock,
  File,
  Files,
  FileImage,
  FileText,
  FileCode,
  Archive,
  Music,
  Video,
  Database,
  HardDrive,
  RefreshCw,
  Cloud,
  Search,
  Filter,
  MoreVertical
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { toast } from "sonner";
import { ClipboardItem, FileTypeInfo, StorageStats } from "../App";

interface ClipboardManagerProps {
  items: ClipboardItem[];
  storageStats: StorageStats | null;
  onClearHistory: () => void;
  onDeleteItem: (itemId: string) => void;
  onCompactStorage: () => void;
  onCopyToClipboard: (content: string) => void;
  onCopyImageToClipboard: (base64Data: string) => void;
  onCopyFilesToClipboard: (filePaths: string[]) => void;
  onSync?: () => Promise<void>;
}

export const ClipboardManager: React.FC<ClipboardManagerProps> = ({
  items,
  storageStats,
  onClearHistory,
  onDeleteItem,
  onCompactStorage,
  onCopyToClipboard,
  onCopyImageToClipboard: _onCopyImageToClipboard,
  onCopyFilesToClipboard,
  onSync,
}) => {
  const [isSyncing, setIsSyncing] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilter, setSelectedFilter] = useState<"all" | "text" | "files">("all");
  
  const handleSync = async () => {
    if (!onSync || isSyncing) return;

    setIsSyncing(true);
    try {
      await onSync();
      toast.success("同步成功！");
    } catch (error) {
      console.error("同步失败:", error);
      toast.error("同步失败，请检查网络连接");
    } finally {
      setIsSyncing(false);
    }
  };

  const handleCopyToClipboard = async (content: string) => {
    try {
      await onCopyToClipboard(content);
      toast.success("已复制到剪贴板");
    } catch (error) {
      toast.error("复制失败");
    }
  };

  const handleCopyFilesToClipboard = async (filePaths: string[]) => {
    try {
      await onCopyFilesToClipboard(filePaths);
      toast.success("文件已复制到剪贴板");
    } catch (error) {
      toast.error("文件复制失败");
    }
  };

  const handleDeleteItem = (itemId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    onDeleteItem(itemId);
    toast.success("项目已删除");
  };

  const handleClearHistory = () => {
    onClearHistory();
    toast.success("历史记录已清空");
  };

  const formatTimestamp = (timestamp: number) => {
    const date = new Date(timestamp * 1000);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60);
      return diffInMinutes === 0 ? "刚刚" : `${diffInMinutes} 分钟前`;
    } else if (diffInHours < 24) {
      return `${Math.floor(diffInHours)} 小时前`;
    } else {
      return date.toLocaleDateString("zh-CN");
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Filter items based on search query and selected filter
  const filteredItems = items.filter(item => {
    const matchesSearch = searchQuery === "" ||
      item.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (item.file_paths && item.file_paths.some(path =>
        path.toLowerCase().includes(searchQuery.toLowerCase())
      ));

    const matchesFilter = selectedFilter === "all" ||
      (selectedFilter === "text" && item.item_type === "text") ||
      (selectedFilter === "files" && item.item_type === "files");

    return matchesSearch && matchesFilter;
  });

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.slice(0, maxLength) + "...";
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "image":
        return <FileImage className="h-4 w-4" />;
      case "video":
        return <Video className="h-4 w-4" />;
      case "audio":
        return <Music className="h-4 w-4" />;
      case "document":
        return <FileText className="h-4 w-4" />;
      case "archive":
        return <Archive className="h-4 w-4" />;
      case "code":
        return <FileCode className="h-4 w-4" />;
      default:
        return <File className="h-4 w-4" />;
    }
  };

  const getItemIcon = (itemType: string) => {
    switch (itemType) {
      case "files":
        return <Files className="h-4 w-4 text-primary" />;
      default:
        return <Copy className="h-4 w-4 text-primary" />;
    }
  };

  const getItemTypeLabel = (itemType: string) => {
    switch (itemType) {
      case "files":
        return "文件";
      default:
        return "文本";
    }
  };

  const handleItemClick = (item: ClipboardItem) => {
    if (item.item_type === "files" && item.file_paths) {
      onCopyFilesToClipboard(item.file_paths);
    } else {
      onCopyToClipboard(item.content);
    }
  };

  const renderFileTypeInfo = (fileTypes: FileTypeInfo[]) => {
    // 按类别分组
    const categorizedFiles = fileTypes.reduce((acc, file) => {
      const category = file.category;
      if (!acc[category]) acc[category] = [];
      acc[category].push(file);
      return acc;
    }, {} as Record<string, FileTypeInfo[]>);

    return (
      <div className="space-y-2">
        {Object.entries(categorizedFiles).map(([category, files]) => (
          <div key={category} className="flex items-center gap-2">
            {getCategoryIcon(category)}
            <span className="text-xs text-muted-foreground capitalize">
              {category} ({files.length})
            </span>
          </div>
        ))}
      </div>
    );
  };

  const renderItemContent = (item: ClipboardItem) => {
    switch (item.item_type) {
      case "files":
        return (
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Files className="h-4 w-4" />
              {item.file_paths?.length} 个文件
            </div>

            {/* 文件类型分类显示 */}
            {item.file_types && item.file_types.length > 0 && (
              <div className="bg-muted/50 rounded-lg p-3">
                {renderFileTypeInfo(item.file_types)}
              </div>
            )}

            {/* 文件列表 */}
            <div className="space-y-2">
              {item.file_paths?.slice(0, 5).map((path, index) => {
                const fileType = item.file_types?.find(ft => ft.path === path);
                const fileName = path.split('/').pop() || path;

                return (
                  <div key={index} className="flex items-center gap-3 p-2 bg-muted/30 rounded-md">
                    <div className="p-1 bg-background rounded">
                      {fileType ? getCategoryIcon(fileType.category) : <File className="h-3 w-3" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate" title={path}>{fileName}</p>
                      {fileType && (
                        <p className="text-xs text-muted-foreground">
                          {fileType.file_type || fileType.category}
                        </p>
                      )}
                    </div>
                  </div>
                );
              })}
              {item.file_paths && item.file_paths.length > 5 && (
                <div className="text-sm text-muted-foreground text-center py-2">
                  还有 {item.file_paths.length - 5} 个文件...
                </div>
              )}
            </div>
            <div className="text-xs text-muted-foreground flex items-center gap-2">
              <Copy className="h-3 w-3" />
              点击复制文件到剪贴板
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-3">
            <div className="bg-muted/30 rounded-lg p-4 border-l-4 border-primary/30">
              <pre className="whitespace-pre-wrap text-sm font-mono overflow-hidden">
                {truncateText(item.content, 200)}
              </pre>
            </div>
            <div className="text-xs text-muted-foreground flex items-center gap-2">
              <Copy className="h-3 w-3" />
              {item.content.length > 200 ? "点击查看完整内容并复制" : "点击复制到剪贴板"}
            </div>
          </div>
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-foreground">剪贴板历史</h2>
          <p className="text-muted-foreground">管理和重复使用你的剪贴板内容</p>
        </div>
        <div className="flex items-center gap-2">
          {onSync && (
            <Button
              onClick={handleSync}
              disabled={isSyncing}
              size="sm"
              className="flex items-center gap-2 material-transition"
            >
              {isSyncing ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Cloud className="h-4 w-4" />
              )}
              {isSyncing ? "同步中..." : "同步"}
            </Button>
          )}
          <Button
            onClick={onCompactStorage}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Database className="h-4 w-4" />
            压缩存储
          </Button>
          <Button
            onClick={handleClearHistory}
            variant="destructive"
            size="sm"
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            清空历史
          </Button>
        </div>
      </div>

      {/* Stats Section */}
      {storageStats && (
        <Card className="material-elevation-1">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Files className="h-4 w-4 text-primary" />
                </div>
                <div>
                  <p className="text-sm font-medium">总项目</p>
                  <p className="text-2xl font-bold">{storageStats.total_items}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-destructive/10 rounded-lg">
                  <Trash2 className="h-4 w-4 text-destructive" />
                </div>
                <div>
                  <p className="text-sm font-medium">已删除</p>
                  <p className="text-2xl font-bold">{storageStats.deleted_items}</p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="p-2 bg-secondary/50 rounded-lg">
                  <HardDrive className="h-4 w-4 text-secondary-foreground" />
                </div>
                <div>
                  <p className="text-sm font-medium">存储大小</p>
                  <p className="text-2xl font-bold">{formatFileSize(storageStats.file_size)}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filter Section */}
      <Card className="material-elevation-1">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索剪贴板内容..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <div className="flex gap-2">
              <Button
                variant={selectedFilter === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter("all")}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                全部
              </Button>
              <Button
                variant={selectedFilter === "text" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter("text")}
                className="flex items-center gap-2"
              >
                <FileText className="h-4 w-4" />
                文本
              </Button>
              <Button
                variant={selectedFilter === "files" ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedFilter("files")}
                className="flex items-center gap-2"
              >
                <Files className="h-4 w-4" />
                文件
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      {/* Items List */}
      {filteredItems.length === 0 ? (
        <Card className="material-elevation-1">
          <CardContent className="flex flex-col items-center justify-center py-16">
            <Copy className="h-16 w-16 text-muted-foreground mb-4" />
            <h3 className="text-xl font-semibold text-muted-foreground mb-2">
              {searchQuery || selectedFilter !== "all" ? "没有找到匹配的项目" : "还没有剪贴板历史"}
            </h3>
            <p className="text-muted-foreground text-center max-w-sm">
              {searchQuery || selectedFilter !== "all"
                ? "尝试调整搜索条件或筛选器"
                : "复制一些文本或文件，它们会自动出现在这里"
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <p className="text-sm text-muted-foreground">
              显示 {filteredItems.length} 个项目 {items.length !== filteredItems.length && `(共 ${items.length} 个)`}
            </p>
          </div>

          <div className="grid gap-4">
            {filteredItems.map((item) => (
              <Card
                key={item.id}
                className="material-elevation-1 hover:material-elevation-2 material-transition cursor-pointer group"
                onClick={() => {
                  if (item.item_type === "files" && item.file_paths) {
                    handleCopyFilesToClipboard(item.file_paths);
                  } else {
                    handleCopyToClipboard(item.content);
                  }
                }}
              >
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-primary/10 rounded-lg">
                        {getItemIcon(item.item_type)}
                      </div>
                      <div>
                        <Badge variant="secondary" className="text-xs">
                          {getItemTypeLabel(item.item_type)}
                        </Badge>
                        {item.size && (
                          <Badge variant="outline" className="text-xs ml-2">
                            {formatFileSize(item.size)}
                          </Badge>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {formatTimestamp(item.timestamp)}
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-destructive hover:text-destructive-foreground"
                        onClick={(e) => handleDeleteItem(item.id, e)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {renderItemContent(item)}
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}; 