import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ExternalLink,
  Server,
  Cloud,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  BookOpen,
  Settings,
  Zap,
  Shield,
  Globe,
  HardDrive,
  Rocket,
  Users,
  DollarSign,
  Clock
} from 'lucide-react';

const steps = [
  {
    id: 1,
    title: "选择存储后端",
    description: "根据你的需求选择合适的云存储服务",
    icon: Cloud
  },
  {
    id: 2,
    title: "获取访问凭证",
    description: "在云服务提供商处创建访问密钥",
    icon: Shield
  },
  {
    id: 3,
    title: "配置连接信息",
    description: "在应用中填入存储配置信息",
    icon: Settings
  },
  {
    id: 4,
    title: "测试并保存",
    description: "测试连接并保存配置开始同步",
    icon: CheckCircle
  }
];

const storageOptions = [
  {
    name: "本地文件系统",
    type: "FileSystem",
    icon: HardDrive,
    badge: { text: "开发测试", variant: "secondary" as const },
    description: "适用于单机使用或开发测试，不支持多设备同步",
    pros: ["无需网络连接", "简单快速", "完全私有"],
    cons: ["不支持多设备同步", "无法备份到云端"],
    difficulty: "简单",
    cost: "免费"
  },
  {
    name: "MinIO (S3兼容)",
    type: "S3Compatible",
    icon: Server,
    badge: { text: "推荐", variant: "default" as const },
    description: "自托管的对象存储服务，完全兼容 Amazon S3 API",
    pros: ["完全控制数据", "高性能", "S3兼容", "可扩展"],
    cons: ["需要自己维护服务器", "需要一定技术能力"],
    difficulty: "中等",
    cost: "服务器成本"
  },
  {
    name: "Amazon S3",
    type: "S3",
    icon: Globe,
    badge: { text: "企业级", variant: "default" as const },
    description: "亚马逊云服务的对象存储，稳定可靠",
    pros: ["高可用性", "全球分布", "企业级安全", "按需付费"],
    cons: ["需要 AWS 账户", "可能有网络延迟"],
    difficulty: "简单",
    cost: "按使用量付费"
  },
  {
    name: "阿里云 OSS",
    type: "Oss",
    icon: Cloud,
    badge: { text: "国内优化", variant: "outline" as const },
    description: "阿里云对象存储服务，国内访问速度快",
    pros: ["国内访问快", "价格便宜", "中文支持好"],
    cons: ["主要服务国内用户", "需要阿里云账户"],
    difficulty: "简单",
    cost: "按使用量付费"
  },
  {
    name: "腾讯云 COS",
    type: "Cos",
    icon: Cloud,
    badge: { text: "国内优化", variant: "outline" as const },
    description: "腾讯云对象存储服务，与微信生态集成好",
    pros: ["国内访问快", "与腾讯生态集成", "价格合理"],
    cons: ["主要服务国内用户", "需要腾讯云账户"],
    difficulty: "简单",
    cost: "按使用量付费"
  },
  {
    name: "Azure Blob Storage",
    type: "AzBlob",
    icon: Globe,
    badge: { text: "企业级", variant: "default" as const },
    description: "微软云服务的对象存储，与 Office 365 集成好",
    pros: ["企业级功能", "与微软生态集成", "全球分布"],
    cons: ["需要 Azure 账户", "配置相对复杂"],
    difficulty: "中等",
    cost: "按使用量付费"
  }
];

export const ConfigGuide: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedStorage, setSelectedStorage] = useState<string | null>(null);

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const progress = (currentStep / steps.length) * 100;

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-3">
          <div className="p-3 bg-primary/10 rounded-lg">
            <BookOpen className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-foreground">配置指南</h2>
            <p className="text-muted-foreground">跟随步骤配置云存储同步</p>
          </div>
        </div>

        {/* Progress */}
        <div className="max-w-md mx-auto space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>步骤 {currentStep} / {steps.length}</span>
            <span>{Math.round(progress)}% 完成</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      </div>

      {/* Step Content */}
      <Card className="material-elevation-1">
        <CardContent className="p-6">
          {/* Step 1: Choose Storage Backend */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <div className="p-3 bg-primary/10 rounded-lg w-fit mx-auto">
                  <Cloud className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">选择存储后端</h3>
                <p className="text-muted-foreground">
                  根据你的需求选择合适的云存储服务
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {storageOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <Card
                      key={option.type}
                      className={`cursor-pointer transition-all hover:material-elevation-2 ${
                        selectedStorage === option.type
                          ? 'ring-2 ring-primary border-primary'
                          : 'hover:border-primary/50'
                      }`}
                      onClick={() => setSelectedStorage(option.type)}
                    >
                      <CardContent className="p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-5 w-5 text-primary" />
                            <h4 className="font-medium">{option.name}</h4>
                          </div>
                          <Badge variant={option.badge.variant}>
                            {option.badge.text}
                          </Badge>
                        </div>

                        <p className="text-sm text-muted-foreground">
                          {option.description}
                        </p>

                        <div className="grid grid-cols-2 gap-2 text-xs">
                          <div className="flex items-center gap-1">
                            <Clock className="h-3 w-3" />
                            <span>{option.difficulty}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3" />
                            <span>{option.cost}</span>
                          </div>
                        </div>

                        <div className="space-y-1">
                          <div className="text-xs font-medium text-green-600">优点:</div>
                          {option.pros.slice(0, 2).map((pro, index) => (
                            <div key={index} className="text-xs text-muted-foreground flex items-center gap-1">
                              <CheckCircle className="h-3 w-3 text-green-500" />
                              {pro}
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          )}

          {/* Step 2: Get Access Credentials */}
          {currentStep === 2 && selectedStorage && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <div className="p-3 bg-primary/10 rounded-lg w-fit mx-auto">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">获取访问凭证</h3>
                <p className="text-muted-foreground">
                  在云服务提供商处创建访问密钥
                </p>
              </div>

              {selectedStorage === 'S3Compatible' && (
                <Alert>
                  <Server className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">MinIO 部署指南:</p>
                      <ol className="list-decimal list-inside space-y-1 text-sm">
                        <li>使用 Docker 部署: <code className="bg-muted px-1 rounded">docker run -p 9000:9000 -p 9001:9001 minio/minio server /data --console-address ":9001"</code></li>
                        <li>访问管理界面: http://localhost:9001</li>
                        <li>创建 Access Key 和 Secret Key</li>
                        <li>创建存储桶 (bucket)</li>
                      </ol>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {selectedStorage === 'S3' && (
                <Alert>
                  <Globe className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">AWS S3 配置指南:</p>
                      <ol className="list-decimal list-inside space-y-1 text-sm">
                        <li>登录 AWS 控制台</li>
                        <li>进入 IAM 服务，创建新用户</li>
                        <li>为用户添加 S3 权限策略</li>
                        <li>生成 Access Key ID 和 Secret Access Key</li>
                        <li>在 S3 服务中创建存储桶</li>
                      </ol>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {(selectedStorage === 'Oss' || selectedStorage === 'Cos') && (
                <Alert>
                  <Cloud className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">国内云服务配置指南:</p>
                      <ol className="list-decimal list-inside space-y-1 text-sm">
                        <li>登录云服务控制台</li>
                        <li>进入对象存储服务</li>
                        <li>创建存储桶 (Bucket)</li>
                        <li>在访问控制中创建 RAM 用户或子账户</li>
                        <li>为用户分配对象存储权限</li>
                        <li>生成 AccessKey 和 SecretKey</li>
                      </ol>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Step 3: Configure Connection */}
          {currentStep === 3 && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <div className="p-3 bg-primary/10 rounded-lg w-fit mx-auto">
                  <Settings className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">配置连接信息</h3>
                <p className="text-muted-foreground">
                  在应用中填入存储配置信息
                </p>
              </div>

              <Alert>
                <Settings className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">配置步骤:</p>
                    <ol className="list-decimal list-inside space-y-1 text-sm">
                      <li>切换到"同步配置"标签页</li>
                      <li>选择对应的存储后端类型</li>
                      <li>填入服务器地址、存储桶名称等信息</li>
                      <li>输入访问密钥 (Access Key) 和密钥 (Secret Key)</li>
                      <li>如需要，在高级设置中调整重试次数和超时时间</li>
                    </ol>
                  </div>
                </AlertDescription>
              </Alert>

              <div className="bg-muted/30 rounded-lg p-4">
                <h4 className="font-medium mb-2 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  安全提示
                </h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 访问密钥具有重要权限，请妥善保管</li>
                  <li>• 建议为应用创建专用的访问密钥，而不是使用主账户密钥</li>
                  <li>• 定期轮换访问密钥以提高安全性</li>
                  <li>• 确保存储桶的访问权限设置正确</li>
                </ul>
              </div>
            </div>
          )}

          {/* Step 4: Test and Save */}
          {currentStep === 4 && (
            <div className="space-y-6">
              <div className="text-center space-y-2">
                <div className="p-3 bg-primary/10 rounded-lg w-fit mx-auto">
                  <CheckCircle className="h-8 w-8 text-primary" />
                </div>
                <h3 className="text-xl font-semibold">测试并保存</h3>
                <p className="text-muted-foreground">
                  测试连接并保存配置开始同步
                </p>
              </div>

              <Alert>
                <Rocket className="h-4 w-4" />
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">最后步骤:</p>
                    <ol className="list-decimal list-inside space-y-1 text-sm">
                      <li>点击"测试连接"按钮验证配置是否正确</li>
                      <li>如果测试成功，点击"保存配置"</li>
                      <li>重启应用以使配置生效</li>
                      <li>开始享受跨设备剪贴板同步功能！</li>
                    </ol>
                  </div>
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-green-50 dark:bg-green-950/20 rounded-lg p-4">
                  <h4 className="font-medium text-green-800 dark:text-green-200 mb-2 flex items-center gap-2">
                    <CheckCircle className="h-4 w-4" />
                    配置成功后
                  </h4>
                  <ul className="text-sm text-green-700 dark:text-green-300 space-y-1">
                    <li>• 剪贴板内容会自动同步到云端</li>
                    <li>• 在其他设备上安装应用并使用相同配置</li>
                    <li>• 所有设备的剪贴板将保持同步</li>
                  </ul>
                </div>

                <div className="bg-blue-50 dark:bg-blue-950/20 rounded-lg p-4">
                  <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2 flex items-center gap-2">
                    <Zap className="h-4 w-4" />
                    使用技巧
                  </h4>
                  <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
                    <li>• 可以在设置中调整同步频率</li>
                    <li>• 支持文本和文件的同步</li>
                    <li>• 可以查看详细的同步状态</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between items-center">
        <Button
          variant="outline"
          onClick={prevStep}
          disabled={currentStep === 1}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          上一步
        </Button>

        <div className="flex items-center gap-2">
          {steps.map((step) => (
            <div
              key={step.id}
              className={`w-2 h-2 rounded-full transition-colors ${
                step.id <= currentStep ? 'bg-primary' : 'bg-muted'
              }`}
            />
          ))}
        </div>

        <Button
          onClick={nextStep}
          disabled={currentStep === steps.length || (currentStep === 1 && !selectedStorage)}
          className="flex items-center gap-2"
        >
          {currentStep === steps.length ? '完成' : '下一步'}
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};